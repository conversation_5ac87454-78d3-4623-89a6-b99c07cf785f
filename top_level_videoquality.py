import time, sys, argparse, os, re, select, csv, datetime
import paramiko
from subprocess import getoutput
import json
from mysqlLib import mySQL
import pandas as pd


parser = argparse.ArgumentParser(formatter_class=argparse.RawTextHelpFormatter)


parser.add_argument('-j',
                    '--job',
                    type=str,
                    default="job1",
                    help='Provide the job name\n\n')
parser.add_argument('-b',
                    '--branch',
                    type=str,
                    default="main",
                    help='Provide the scripts branch name\n\n')
parser.add_argument('-g',
                    '--gaudibranch',
                    type=str,
                    default="RANGER_CM133_v6.0",
                    help='Provide the Gaudi compile branch name\n\n')
parser.add_argument('-f',
                    '--configbranch',
                    type=str,
                    default="master",
                    help='Provide the config files branch name\n\n')
parser.add_argument('-e',
                    '--enable_defination',
                    type=str,
                    default="",
                    help='Provide a defination to enable, ex: add_definitions(-DNETINT_RANGER_RC_V1_TAV)\n\n')
parser.add_argument('-d',
                    '--disable_defination',
                    type=str,
                    default="",
                    help='Provide a defination to disable, ex: add_definitions(-DNETINT_RANGER_RC_V1_TAV)\n\n')
parser.add_argument("--twopass", action="store_true", help="Run two pass test")
parser.add_argument("--custom_Gaudi", action="store_true", help="Run two pass test")
parser.add_argument("--save_to_db", action="store_true", help="To save data to DB")
parser.add_argument('-c',
                    '--compile_flag',
                    type=str,
                    default="",
                    help='Add compile flag\n\n')
parser.add_argument('-c1', 
					'--config1',
					type=str,
					default="NONE.cfg",
                    help='Pass1 Config parameter\n\n')
parser.add_argument('-c2', 
					'--config2',
					type=str,
					default="NONE.cfg",
                    help='Pass2 Config parameter\n\n')
parser.add_argument('-c3', 
					'--config3',
					type=str,
					default="NONE.cfg",
                    help='Pass3 Config parameter\n\n')
parser.add_argument('-c4', 
					'--config4',
					type=str,
					default="NONE.cfg",
                    help='Pass4 Config parameter\n\n')
parser.add_argument('-c5', 
					'--config5',
					type=str,
					default="NONE.cfg",
                    help='Pass5 Config parameter\n\n')
parser.add_argument('-v', 
					'--Compare_with',
					type=str,
					default="RANGER_CM_PX4_156_v2.0.0",
                    help='Provide branch name to compare with\n\n')
parser.add_argument('-i', 
					'--config_file',
					type=str,
					default="BQTC_12_2024.cfg",
                    help='Provide config file to compare with\n\n')			
args = parser.parse_args()
compile_flag = args.compile_flag

def ensure_cfg_extension(config):
    if not config.endswith('.cfg'):
        return config + '.cfg'
    return config

config1 = ensure_cfg_extension(args.config1)
config2 = ensure_cfg_extension(args.config2)
config3 = ensure_cfg_extension(args.config3)
config4 = ensure_cfg_extension(args.config4)
config5 = ensure_cfg_extension(args.config5)

enable_defination = args.enable_defination
disable_defination = args.disable_defination

host_server = "YVR1LabSlurm04"
user = "nvme"
password = "logan"

def print_now(text):
	with open('scriptlog.txt','a') as outputFile:
		outputFile.write(time.strftime("%Y-%m-%d %H:%M:%S") + ' '*7  + str(text) + '\n')
	print('{}'.format(text))
	sys.stdout.flush()

def count_passes():
    # Count the number of non-NONE pass values
    passes = 0
    for pass_value in [config1, config2, config3, config4, config5]:
        if pass_value != "NONE.cfg":
            passes += 1
    return passes

def connectHost(timeoutloop=5):
	ssh = paramiko.SSHClient()
	time.sleep(1)
	ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
	time.sleep(1)
	for i in range(timeoutloop):
		start_time = time.time()
		try:
			ssh.connect( host_server , username = user, password = password , timeout = 60, look_for_keys=False, allow_agent=False, banner_timeout=60)
			ssh.get_transport().set_keepalive(30)
			return ssh
		except Exception as e:
			print_now(f"SSH Retry {i+1}/{timeoutloop}")
			sleep_time = 60 - (time.time() - start_time)
			if sleep_time > 5:
				time.sleep(sleep_time)
			else:
				time.sleep(5)
	print_now("SSH Connect Failed")
	return False

def exec_command(cmd, print_cmd=True):
	ssh = connectHost()
	if print_cmd:
		print_now(cmd)
		try:
			stdin, stdout, stderr = ssh.exec_command(cmd, get_pty=True)
			stdout.channel.setblocking(0)
			stderr.channel.setblocking(0)

			while not stdout.channel.exit_status_ready():
				# Use select to wait for data to be available
				rl, wl, xl = select.select([stdout.channel, stderr.channel], [], [], 1.0)
				if stdout.channel in rl:
					line = stdout.readline()
					if line:
						print_now(line.strip())
				
				time.sleep(1)  # Prevent busy-waiting

			exit_status = stdout.channel.recv_exit_status()
			return exit_status

		except Exception as e:
			print_now(f"Error executing command: {e}")
			return None
		finally:
			ssh.close()
	else:
		try:
			stdin, stdout, stderr = ssh.exec_command(cmd, get_pty=True)
			output = stdout.read().decode("utf-8")
			exit_status = stdout.channel.recv_exit_status()
			return output.strip(), exit_status
		except Exception as e:
			print_now(f"Error executing command: {e}")
			return None, None
		finally:
			ssh.close()
		
def disable_definition_by_line(cmake_file, line_to_disable):
	cmd = f"sed -i '/^{line_to_disable.strip()}/s/^/#/' {cmake_file}"
	output, exit_status = exec_command(cmd,print_cmd=False)
	if exit_status == 0:
		print(f"Successfully disabled the line: {line_to_disable}")
	else:
		print(f"Failed to disable the line: {line_to_disable}")
def enable_definition_by_line(cmake_file, line_to_enable):
	cmd = f"sed -i '/^#{line_to_enable.strip()}/s/^#//' {cmake_file}"
	output,exit_status = exec_command(cmd,print_cmd=False)
	if exit_status == 0:
		print(f"Successfully enabled the line: {line_to_enable}")
	else:
		print(f"Failed to enable the line: {line_to_enable}")
def enable_or_disable_definations(Video_ip_fw):
	cmake_file = f"{Video_ip_fw}/CMakeLists.txt"
	if enable_defination:
		enable_definations = enable_defination.split(',')
		for enable_def in enable_definations:
			enable_definition_by_line(cmake_file,enable_def)
	if disable_defination:
		disable_definations = disable_defination.split(',')
		for disable_def in disable_definations:
			disable_definition_by_line(cmake_file,disable_def)
	

def upload_file(local_path, remote_path):
	print_now("\nUploading files to job server\n")
	ssh = connectHost()
	if ssh:
		try:
			sftp = ssh.open_sftp()
			if os.path.exists(local_path):
				sftp.put(local_path, remote_path)
				sftp.close()
				print_now(f"File uploaded from {local_path} to {remote_path}")
		except Exception as e:
			print_now(f"File upload failed: {str(e)}")
		finally:
			ssh.close()
	else:
		print_now("Upload failed: connection to host failed")

def generate_csv(core_filename,metadata):
	with open(core_filename, mode='r', newline='') as core_file:
		reader = csv.reader(core_file)
		headers = next(reader)
		with open('result.csv', mode='w', newline='') as updated_file:
			writer = csv.writer(updated_file)
			writer.writerow(['DATE', 'videoip_branch', 'Videoip_hash','cfg_branch', 'cfg_files_hash', 'cfg_file_name','test_type', 'withTAV'] + headers)
			for row in reader:
				writer.writerow([
                metadata['DATE'], metadata['videoip_branch'], metadata['Videoip_hash'], metadata['cfg_branch'],metadata['cfg_files_hash'],
                metadata['cfg_file_name'], metadata['test_type'],metadata['withTAV']
             ] + row)

def store_to_db(path_to_csv):
	db = mySQL()
	db.insertCSVData(path_to_csv, 'Results')

def download_file(remote_file_path, local_file_path):
	ssh = connectHost()
	if ssh:
		try:
			sftp = ssh.open_sftp()
			local_file_path = os.path.join(local_file_path, os.path.basename(remote_file_path))
			sftp.get(remote_file_path, local_file_path)
			print(f"Downloaded: {remote_file_path} to {local_file_path}")
			sftp.close()
		except Exception as e:
			print(f"Download failed: {str(e)}")
		finally:
			ssh.close()

def check_tav(config_file_path):
	withTAV = False
	try:
		cmd = f"cat {config_file_path}"
		output, exit_status = exec_command(cmd,print_cmd=False)
		if exit_status == 0:
			for line in output.splitlines():
				line = line.strip()
				if "AccurateRC" in line:
					key, value = line.split('=')
					key = key.strip()
					value = value.split(';')[0].strip()
					if key == "AccurateRC" and value == "1":
						withTAV = True
						break
		else:
			print(f"Failed to read the file: {config_file_path}")
	except FileNotFoundError:
		print(f"{config_file_path} not found.")
	return withTAV

def copy_files_to_job(job_dir,Video_ip_fw, num_passes):
	print_now("\n----Copying required files to job----\n")
	#copy files from regression_quick_quality_scripts to job
	quality_scripts_dir = f"{job_dir}/regression_quick_quality_scripts/*"
	simulations_dir = "/mnt/ceph/Automation/simulations/*"
	exec_command(f"cp -r {quality_scripts_dir} {job_dir}")
	#copy binaries from simulation directory to job
	exec_command(f"cp -r {simulations_dir} {job_dir}")
	#copy config file
	exec_command(f"cp -r {job_dir}/regression_cfg/quick_quality_cfg_files/* {job_dir}/quality_cfg/")
	if not args.custom_Gaudi:
		#Copy Gaudi binary from Video_ip_fw
		exec_command(f"find {Video_ip_fw} -type f -name 'Gaudi*' -executable -exec cp {{}} {job_dir}/Gaudi \;")

	for i in range(1, num_passes):
		pass_dir = os.path.join(job_dir, f"pass{i}")
		exec_command(f"mkdir -p {pass_dir}", print_cmd=False)
		exec_command(f"cp -r {quality_scripts_dir} {pass_dir}")
		exec_command(f"cp -r {simulations_dir} {pass_dir}")
		exec_command(f"cp -r {job_dir}/regression_cfg/quick_quality_cfg_files/* {pass_dir}/quality_cfg/")
		if not args.custom_Gaudi:
			exec_command(f"find {Video_ip_fw} -type f -name 'Gaudi*' -executable -exec cp {{}} {pass_dir}/Gaudi \;")
		

def compare_bdrate(with_TAV,video_ip_branch,config_filename):
	pd.set_option('display.max_rows', None)   # Show all rows
	pd.set_option('display.max_columns', None)  # Show all columns
	pd.set_option('display.width', None)      # Don't wrap lines
	pd.set_option('display.max_colwidth', None)  # Show full content in each column (no truncation)

	withTAV = 1 if with_TAV else 0

	# Define the list of codecs and sequences
	codecs = ['av1', 'h265', 'h264']
	sequences = [
		'comic_ElephantsDream_1280x720p24',
		'game_Gujian_1280x720p60',
		'mtv_ChineseSpringFestivalGala_1280x720p25',
		'scenery_TaiPei_1280x720p30'
	]

	# Fetch data from the database
	query = f"""
	SELECT
		CODEC,
		SEQUENCE,
		MIN(BDRATE_PSNR) AS BDRATE_PSNR,
		MIN(BDRATE_SSIM) AS BDRATE_SSIM,
		MIN(BDRATE_VMAF) AS BDRATE_VMAF
	FROM
		Quality_Results.Results
	WHERE
		withTAV = {withTAV}
		AND videoip_branch = '{video_ip_branch}'
		AND cfg_file_name = '{config_filename}'
	GROUP BY
		CODEC,
		SEQUENCE;
	"""

	try:
		db = mySQL()
		db_data = db.sqlExecute(query)
	except Exception as e:
		print(f"Error fetching data from the database: {e}")
		db_data = pd.DataFrame()
	columns = ['CODEC', 'SEQUENCE', 'BDRATE_PSNR', 'BDRATE_SSIM', 'BDRATE_VMAF']
	db_data = pd.DataFrame(db_data, columns=columns)
	for col in ['BDRATE_PSNR', 'BDRATE_SSIM', 'BDRATE_VMAF']:
		db_data[col] = db_data[col].apply(float)

	# Read and filter data from the CSV file
	try:
		csv_data = pd.read_csv('result.csv')
	except Exception as e:
		print(f"Error reading CSV file: {e}")
		csv_data = pd.DataFrame()
	csv_data = csv_data.groupby(['CODEC', 'SEQUENCE']).agg({
		'BDRATE_PSNR': 'min',
		'BDRATE_SSIM': 'min',
		'BDRATE_VMAF': 'min'
	}).reset_index()
	comparison_results = []

	for codec in codecs:
		for sequence in sequences:
			db_row = db_data[(db_data['CODEC'] == codec) & (db_data['SEQUENCE'] == sequence)]
			csv_row = csv_data[(csv_data['CODEC'] == codec) & (csv_data['SEQUENCE'] == sequence)]
			if not db_row.empty and not csv_row.empty:
				# Extract
				db_values = db_row.iloc[0]
				csv_values = csv_row.iloc[0]
				# Compare and store results
				comparison_entry = {
					'CODEC': codec,
					'SEQUENCE': sequence
				}
				for metric in ['BDRATE_PSNR', 'BDRATE_SSIM', 'BDRATE_VMAF']:
					db_value = db_values[metric]
					csv_value = csv_values[metric].round(2)
					diff = round(db_value - csv_value, 2)
					comparison_entry[f'{metric}_db'] = db_value
					comparison_entry[f'{metric}_csv'] = csv_value
					comparison_entry[f'{metric}_diff'] = diff
				comparison_results.append(comparison_entry)
	comparison_df = pd.DataFrame(comparison_results)
	comparison_df = comparison_df.rename(columns={
		'BDRATE_PSNR_db': 'BD_PSNR_db',
		'BDRATE_PSNR_csv': 'BD_PSNR_csv',
		'BDRATE_PSNR_diff': 'BD_PSNR_diff',
		'BDRATE_SSIM_db': 'BD_SSIM_db',
		'BDRATE_SSIM_csv': 'BD_SSIM_csv',
		'BDRATE_SSIM_diff': 'BD_SSIM_diff',
		'BDRATE_VMAF_db': 'BD_VMAF_db',
		'BDRATE_VMAF_csv': 'BD_VMAF_csv',
		'BDRATE_VMAF_diff': 'BD_VMAF_diff'
	})
	print("\n")
	print(comparison_df.to_string(index=False))
	print("\n")
	average_df = comparison_df.groupby('CODEC').agg({
		'BD_PSNR_db': 'mean',
		'BD_PSNR_csv': 'mean',
		'BD_SSIM_db': 'mean',
		'BD_SSIM_csv': 'mean',
		'BD_VMAF_db': 'mean',
		'BD_VMAF_csv': 'mean'
	}).reset_index()

	# Calculate the differences between database and CSV averages
	average_df['BD_PSNR_diff'] = average_df['BD_PSNR_db'] - average_df['BD_PSNR_csv']
	average_df['BD_SSIM_diff'] = average_df['BD_SSIM_db'] - average_df['BD_SSIM_csv']
	average_df['BD_VMAF_diff'] = average_df['BD_VMAF_db'] - average_df['BD_VMAF_csv']

	# Rename the columns for clarity
	average_df = average_df.rename(columns={
		'BD_PSNR_db': 'Avg_BD_PSNR_db',
		'BD_PSNR_csv': 'Avg_BD_PSNR_csv',
		'BD_SSIM_db': 'Avg_BD_SSIM_db',
		'BD_SSIM_csv': 'Avg_BD_SSIM_csv',
		'BD_VMAF_db': 'Avg_BD_VMAF_db',
		'BD_VMAF_csv': 'Avg_BD_VMAF_csv'
	})

	# Reorder the columns to place the diff columns immediately after their corresponding metrics

	average_df = average_df[[
		'CODEC',
		'Avg_BD_PSNR_db', 'Avg_BD_PSNR_csv', 'BD_PSNR_diff',
		'Avg_BD_SSIM_db', 'Avg_BD_SSIM_csv', 'BD_SSIM_diff',
		'Avg_BD_VMAF_db', 'Avg_BD_VMAF_csv', 'BD_VMAF_diff'
	]]
	if (average_df['BD_PSNR_diff'].abs() > 1).any() or \
	   (average_df['BD_SSIM_diff'].abs() > 1).any() or \
	   (average_df['BD_VMAF_diff'].abs() > 1).any():
	   print("quality difference is more then 1%")
	# Print the new table with average values and differences
	print(average_df.to_string(index=False))

def ensure_job_directory(base_dir, job_name):
    """Ensures the job directory exists, and if not, creates it or increments the name."""
    print_now("\n")
    original_job_name = job_name
    counter = 1
    while True:
        cmd = "mkdir -p" if "/" in job_name else "mkdir"
        output, exit_code = exec_command(f"cd {base_dir} && {cmd} {job_name}", print_cmd=False)
        if exit_code == 0:
            return job_name
        else:
            job_name = f"{original_job_name}_{counter}"
            counter += 1

def check_job_status(job_id):
	while True:
		cmd = f"squeue -j {job_id}"
		output, exit_code = exec_command(cmd, print_cmd=False)
		if exit_code == 0:
			if job_id not in output:
				print_now("Job has completed\n")
				break
		time.sleep(30)

def git_steps(job_dir,branch,url):
	cmd = f"git clone --depth 1 -b {branch} {url}"
	exec_command(f"cd {job_dir} && {cmd}",print_cmd=False)
	print("Successfully Checkout")

def get_hash(repo_dir):
	cmd = f"git -C {repo_dir} rev-parse HEAD"
	output, exit_code = exec_command(cmd,print_cmd=False)
	return output

def running_cmds(job_dir,cmd):
	cmd = f"cd {job_dir} && " + cmd
	output, exit_code = exec_command(cmd)
	print(output)
	if exit_code != 0:
		print_now("Failed to execute commands")
		sys.exit(1)
		
def Build_and_compile_gaudi(Video_ip_fw):
	print_now("\n----Compiling Gaudi----")
	output, exit_status = exec_command(f'cd {Video_ip_fw}/build && chmod 777 *.sh && ./cmake.sh {compile_flag}', print_cmd=False)
	if exit_status == 0:
		print_now("\nSuccessfully Compiled Gaudi")

total_passes = count_passes()

if args.twopass:
	final_config = next(config for config in [config5, config4, config3, config2, config1] if config != "NONE.cfg")
else:
	final_config = config1
def report_generation():
	print(f"Host - {host_server} User - {user}, Password - {password}")
	if total_passes == 0:
		print_now("No config file selected")
		sys.exit(1)
	base_dir = "/mnt/ceph/Automation/quick_quality_test"
	job_name = ensure_job_directory(base_dir, args.job)
	job_dir = f"{base_dir}/{job_name}"
	print("job directory: ", job_dir)
	regression_quick_quality_scripts = "https://git.video:'r65j%4!oBUma'@git.netint.ca/quality_tests/regression_quick_quality_scripts.git"
	video_ip_ranger = "https://git.video:'r65j%4!oBUma'@git.netint.ca/vit/video_ip_ranger.git"
	regression_cfg = "https://git.video:'r65j%4!oBUma'@git.netint.ca/vit/regression_cfg.git"
	firmware = f"{job_dir}/regression_quick_quality_scripts"
	Video_ip_fw = f"{job_dir}/video_ip_ranger"
	try:
		print_now("\n----Checkout regression_quick_quality_scripts branch to get vqe files----")
		git_steps(job_dir,args.branch,regression_quick_quality_scripts)
		if not args.custom_Gaudi:
			print_now("\n----Checkout video_ip branch to compile Gaudi----")
			git_steps(job_dir,args.gaudibranch, video_ip_ranger)
			enable_or_disable_definations(Video_ip_fw)
			Build_and_compile_gaudi(Video_ip_fw)
		print_now("\n----Checkout CFG branch to get cfg files----")
		git_steps(job_dir,args.configbranch, regression_cfg)
		copy_files_to_job(job_dir,Video_ip_fw,total_passes)
		print("\n")
		with open("logPathSanity.txt", "a") as f:
			for i in range(total_passes):
				config = globals()[f"config{i+1}"]
				job_pass_name = job_name if i == total_passes - 1 else os.path.join(job_name, f"pass{i+1}")
				job_pass_dir = os.path.join(base_dir, job_pass_name)
				upload_file(f"{config}",f"{job_pass_dir}/quality_cfg/{config}")
				if args.custom_Gaudi:
					upload_file(f"Gaudi",f"{job_pass_dir}/Gaudi")
					output, exit_status = exec_command(f"chmod +x {job_pass_dir}/Gaudi",print_cmd=False)
				cmd = f"python3 gaudi_quality.py -p {config} -j {job_pass_name} -t {job_name} --run_jobs"
				if args.twopass:
					cmd += " --encoding_only" if i != total_passes - 1 else " --create_detailed_report"
				else:
					cmd += " --create_detailed_report"
				print(cmd)
				f.write(f"\nUse Windows file explorer to open log files of jenkins at below link for pass{i+1}:\n\n")
				f.write('\\\\************\\video-quality-evaluation-testResult\\quick_quality_test\\{}\n'.format(job_pass_name))

				f.write("\nUse Linux file explorer to open log files of jenkins at below link:\n\n")
				f.write('//************/video-quality-evaluation-testResult/quick_quality_test/{}\n'.format(job_pass_name))

				exit_code = exec_command(f"cd {job_pass_dir} && {cmd}")

				if exit_code == 10:
					print_now(f"Config file validation failed")
					sys.exit(1)
				print(exit_code)
				#elif exit_code != 0:
				#	print_now(f"Failed to execute commands {cmd}")
				#	sys.exit(1)
		cmake_file = f"{Video_ip_fw}/CMakeLists.txt"
		final_config_path = f"{job_name}/quality_cfg/{final_config}"
		withTAV = check_tav(final_config_path)
		remote_file_path = f"{job_dir}/data.csv"
		local_directory = os.getcwd()
		download_file(remote_file_path,local_directory)
		today_date = datetime.date.today().isoformat()
		videoip_tag = args.gaudibranch
		videoip_hash = get_hash(Video_ip_fw)
		cfg_branch = args.configbranch
		cfgbranch_hash = get_hash(f"{job_dir}/regression_cfg")
		cfg_key = 'config1' if not args.twopass else f"config{total_passes}"
		cfg_file = globals().get(cfg_key, None)
		excel_sheet = f"{job_dir}/cnm_wave677_{cfg_file.split('.')[0]}_self.xlsx"
		download_file(excel_sheet,local_directory)
		test_type = "1Pass" if not args.twopass else "2Pass"
		metadata = {
			'DATE': today_date,
			'videoip_branch': videoip_tag,
			'Videoip_hash': videoip_hash,
			'cfg_branch': cfg_branch,
			'cfg_files_hash': cfgbranch_hash,
			'cfg_file_name': cfg_file,
			'test_type': test_type,
			'withTAV': withTAV
		}
		core_file = f"{local_directory}/data.csv"
		generate_csv(core_file,metadata)
		if args.save_to_db:
			store_to_db(f"{local_directory}/result.csv")
		compare_bdrate(withTAV,args.Compare_with,args.config_file)
	finally:
		os.makedirs("archive")
		getoutput(f"mv data.csv archive/")
		getoutput(f"mv logPathSanity.txt archive/")
		cfg_key = 'config1' if not args.twopass else f"config{total_passes}"
		cfg_file = globals().get(cfg_key, None)
		getoutput(f"mv cnm_wave677_{cfg_file.split('.')[0]}_self.xlsx archive/")



if __name__ == "__main__":
	report_generation()